package com.ebon.energy.fms.service.site;

import com.ebon.energy.fms.common.utils.Helper;
import com.ebon.energy.fms.domain.vo.DataWithPermalinkVO;
import com.ebon.energy.fms.domain.vo.site.*;
import com.ebon.energy.fms.service.SiteDeviceService;
import com.ebon.energy.fms.service.SiteService;
import com.ebon.energy.fms.service.factory.EnergyFlowFactory;
import com.ebon.energy.fms.service.factory.ISiteConfigurationAggregator;
import com.ebon.energy.fms.service.factory.SiteConfigurationAggregatorFactory;
import com.ebon.energy.fms.util.ConfigurationUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SiteEnergyService {

    private final SiteService siteService;

    private final SiteDeviceService siteDeviceService;

    private final SiteConfigurationAggregatorFactory siteConfigurationAggregatorFactory;


    public EnergyFlowDto getEnergyFlow(String siteId) {
        var exportLimitMessageThresholdW = ConfigurationUtil.getPortalConfig().getExportLimitMessageThresholdW();

        return null;
    }

    public BatteryStatusDto getBatteryStatus(String siteId) {
        return null;
    }

    public RenewableGaugeDto getRenewableGauge(String siteId) {
        return null;
    }


    public EnergyFlowDto getSiteEnergyFlow(String siteId, Integer globalCurrentGridExportLimit) {
        var siteDevices = siteDeviceService.getSiteDevices(siteId);
        var siteAggregator = siteConfigurationAggregatorFactory.getSiteAggregator(siteId, siteDevices);
        var allAbout = siteAggregator.processSiteOverview(siteId, siteDevices);
        if (allAbout.getDataForDashboard() == null) {
            return null;
        }

        var energyFlowExtended = EnergyFlowFactory.buildForViewModels(allAbout.getDataForDashboard(),
                allAbout.getHasBatteries(), allAbout.getHasSupportForConnectedPV());

        Helper.getEnergyFlowFromParts()
    }


}
